package com.sfn.model;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class RetryConfig {
	
   boolean isReliable=true;
   private List<String> reliableErrorCodes;
   private List<String> interval;

   public RetryConfig(String reliableErrorCodes, String interval, String delimiter) {

      if(null == reliableErrorCodes || reliableErrorCodes == "") {
         this.reliableErrorCodes = Collections.EMPTY_LIST;
      } else {
         this.reliableErrorCodes = Arrays.asList(reliableErrorCodes.split(delimiter));
      }

      if(null == interval || interval == "") {
         this.interval  = Collections.EMPTY_LIST;
      } else {
         this.interval = Arrays.asList(interval.split(delimiter));
      }
   }
}
