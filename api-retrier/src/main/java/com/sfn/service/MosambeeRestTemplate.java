package com.sfn.service;

import com.sfn.exception.CircuitBrokenException;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

@Log4j2
public class MosambeeRestTemplate<R> {

    private RestTemplate restTemplate;
    private final CircuitBreakerManager breakerManager;
    private int learningRequestCount;
    private long baseBlockDurationMs;
    private long latencySpikeThresholdMs;
    private long windowMs;

    public MosambeeRestTemplate(RestTemplate restTemplate, int learningRequestCount, long baseBlockDurationMs, long latencySpikeThresholdMs, long windowMs) {
        this.restTemplate = restTemplate;
        this.learningRequestCount = learningRequestCount;
        this.baseBlockDurationMs = baseBlockDurationMs;
        this.latencySpikeThresholdMs = latencySpikeThresholdMs;
        this.windowMs = windowMs;
        breakerManager = new CircuitBreakerManager(learningRequestCount, baseBlockDurationMs, latencySpikeThresholdMs, windowMs);

    }

    public ResponseEntity<R> exchange(String url, HttpMethod method, HttpEntity<Object> request, Class<R> clazz) {
        if (breakerManager.isBlocked(url)) {
            throw new CircuitBrokenException(url, "URL temporarily blocked: " + url);
        }

        long start = System.currentTimeMillis();
        try {
            ResponseEntity<R> response = restTemplate.exchange(url, method, request, clazz);

            breakerManager.recordLatency(url, System.currentTimeMillis() - start);
            return response;
        } catch (Exception ex) {
            breakerManager.recordFailure(url);
            log.error("Failure encountered, increasing failure count for : " + url);
            throw ex;
        }
    }
}