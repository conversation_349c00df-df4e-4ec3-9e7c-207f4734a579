package com.sfn.service;


import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

@Log4j2
public class InitiateApi<R> {

	public ResponseEntity<R> sendRequest(MosambeeRestTemplate restTemplate, String url, HttpEntity entity, HttpMethod method, Class<R> clazz) {

		ResponseEntity<R> response = restTemplate.exchange(url, method, entity, clazz);
		return response;
	}
}