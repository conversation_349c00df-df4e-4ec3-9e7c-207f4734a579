package com.sfn.service;

import lombok.extern.log4j.Log4j2;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedDeque;

@Log4j2
public class AdaptiveCircuitState {

    private static int LEARNING_REQUEST_COUNT = 5;
    private static long BASE_BLOCK_DURATION_MS = 30_000;
    private static long LATENCY_SPIKE_THRESHOLD_MS = 3000;
    private static long WINDOW_MS = 60_000;

    private int requestCount = 0;
    private long blockedUntil = 0;

    private final Deque<Long> failureTimestamps = new ConcurrentLinkedDeque<>();
    private final Deque<LatencyEntry> latencyHistory = new ConcurrentLinkedDeque<>();

    private double emaLatency = -1.0;
    private static final double EMA_ALPHA = 0.2;

    public AdaptiveCircuitState(String url, int learningRequestCount, long baseBlockDurationMs, long latencySpikeThresholdMs, long windowMs) {
        log.info("Initializing circuit breaker for URL: {}", url);
        LEARNING_REQUEST_COUNT = learningRequestCount;
        BASE_BLOCK_DURATION_MS = baseBlockDurationMs;
        LATENCY_SPIKE_THRESHOLD_MS = latencySpikeThresholdMs;
        WINDOW_MS = windowMs;
    }



    public boolean isBlocked() {
        long now = System.currentTimeMillis();

        if (now < blockedUntil) return true;
        if (requestCount < LEARNING_REQUEST_COUNT) return false;

        cleanupOld(failureTimestamps, now);
        cleanupOldLatencies(latencyHistory, now);

        if (failureTimestamps.size() >= getFailureThreshold()) {
            blockedUntil = now + getBlockDuration();
            log.debug("[circuit-breaker] Blocking request till {} due to failures. Threshold: {}", blockedUntil, getFailureThreshold());
            return true;
        }

        if (countLatencySpikes(now) >= getLatencySpikeThreshold()) {
            blockedUntil = now + getBlockDuration();
            log.debug("[circuit-breaker] Blocking request till {} due to latency. Threshold: {}", blockedUntil, getLatencySpikeThreshold());

            return true;
        }

        return false;
    }

    public void recordFailure() {
        requestCount++;
        failureTimestamps.add(System.currentTimeMillis());
        log.debug("[circuit-breaker] Failed request recorded. Count increased to {}", failureTimestamps.size());
    }

    public void recordLatency(long latencyMs) {
        requestCount++;

        long now = System.currentTimeMillis();

        synchronized (latencyHistory) {
            latencyHistory.add(new LatencyEntry(now, latencyMs));
        }

        if (emaLatency < 0) emaLatency = latencyMs;
        else emaLatency = EMA_ALPHA * latencyMs + (1 - EMA_ALPHA) * emaLatency;
    }

    private int getFailureThreshold() {
        if (requestCount < 50) return 3;
        else if (requestCount < 200) return 6;
        else return 10;
    }

    private int getLatencySpikeThreshold() {
        if (requestCount < 50) return 3;
        else if (requestCount < 200) return 6;
        else return 10;
    }

    private long getBlockDuration() {
        if (requestCount < 100) return BASE_BLOCK_DURATION_MS;
        return BASE_BLOCK_DURATION_MS + 10000;
    }

    private void cleanupOld(Deque<Long> timestamps, long now) {
        synchronized (timestamps) {
            while (true) {
                Long ts = timestamps.peekFirst();
                if (ts == null || now - ts <= WINDOW_MS) break;
                timestamps.pollFirst();
            }
        }
    }

    private void cleanupOldLatencies(Deque<LatencyEntry> latencies, long now) {
        synchronized (latencies) {
            while (true) {
                LatencyEntry entry = latencies.peekFirst();
                if (entry == null || now - entry.timestamp <= WINDOW_MS) break;
                latencies.pollFirst();
            }
        }
    }

    private int countLatencySpikes(long now) {
        int count = 0;
        List<LatencyEntry> snapshot;
        synchronized (latencyHistory) {
            snapshot = new ArrayList<>(latencyHistory);
        }

        for (LatencyEntry entry : snapshot) {
            if (now - entry.timestamp <= WINDOW_MS &&
                    entry.latencyMs > Math.max(emaLatency * 1.5, LATENCY_SPIKE_THRESHOLD_MS)) {
                count++;
            }
        }
        return count;
    }

    private static class LatencyEntry {
        long timestamp;
        long latencyMs;

        LatencyEntry(long timestamp, long latencyMs) {
            this.timestamp = timestamp;
            this.latencyMs = latencyMs;
        }
    }
}