package com.sfn.service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class CircuitBreakerManager {

    private int learningRequestCount;
    private long baseBlockDurationMs;
    private long latencySpikeThresholdMs;
    private long windowMs;
    private final Map<String, AdaptiveCircuitState> circuits = new ConcurrentHashMap<>();

    public CircuitBreakerManager(int learningRequestCount, long baseBlockDurationMs, long latencySpikeThresholdMs, long windowMs) {
        this.learningRequestCount = learningRequestCount;
        this.baseBlockDurationMs = baseBlockDurationMs;
        this.latencySpikeThresholdMs = latencySpikeThresholdMs;
        this.windowMs = windowMs;
    }

    public boolean isBlocked(String url) {
        return circuits.computeIfAbsent(url, urlKey -> new AdaptiveCircuitState(
            urlKey,
                learningRequestCount,                // learningRequestCount
                baseBlockDurationMs,           // baseBlockDurationMs
                latencySpikeThresholdMs,             // latencySpikeThresholdMs
                windowMs            // windowMs
        )).isBlocked();
    }

    public void recordFailure(String url) {
        circuits.computeIfAbsent(url, urlKey -> new AdaptiveCircuitState(
                urlKey,
                learningRequestCount,                // learningRequestCount
                baseBlockDurationMs,           // baseBlockDurationMs
                latencySpikeThresholdMs,             // latencySpikeThresholdMs
                windowMs            // windowMs
        )).recordFailure();
    }

    public void recordLatency(String url, long latencyMs) {
        circuits.computeIfAbsent(url, urlKey -> new AdaptiveCircuitState(
                urlKey,
                learningRequestCount,                // learningRequestCount
                baseBlockDurationMs,           // baseBlockDurationMs
                latencySpikeThresholdMs,             // latencySpikeThresholdMs
                windowMs            // windowMs
        )).recordLatency(latencyMs);
    }
}
