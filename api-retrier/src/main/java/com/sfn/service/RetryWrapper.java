package com.sfn.service;

import com.sfn.model.RetryConfig;
import com.sfn.model.RetryLog;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

@Log4j2
public class RetryWrapper<R> {

	InitiateApi<R> initiateApi;
	MosambeeRestTemplate restTemplate;

	public RetryWrapper(MosambeeRestTemplate restTemplate) {
		this.initiateApi = new InitiateApi();
		this.restTemplate = restTemplate;
	}

	public List<RetryLog<R>> sendData(String url, HttpMethod method, HttpEntity entity, RetryConfig retryConfig, int count, Class<R> clazz) {

		Assert.notNull(retryConfig, "Retry Config is null");
		Assert.notNull(url, "URL is null");
		Assert.notNull(method, "HTTP Method is null");

		List<RetryLog<R>> retryLogs = new ArrayList<>();

		ResponseEntity<R> initiateApiRespnse = initiateApi.sendRequest(restTemplate, url, entity, method, clazz);
		log.debug("get data from initiate api response : {}", initiateApiRespnse);

		count++;
		retryLogs.add(new RetryLog<R>(count, initiateApiRespnse));

		if(!retryConfig.getReliableErrorCodes().contains("!"+initiateApiRespnse.getStatusCode().value()) || retryConfig.getReliableErrorCodes().contains(""+initiateApiRespnse.getStatusCode().value())) {
			if (count <= retryConfig.getInterval().size()) {
				try {
					Thread.sleep(Long.parseLong(retryConfig.getInterval().get(count - 1)));
				} catch (InterruptedException e) {
					throw new RuntimeException(e);
				}
				retryLogs.addAll(sendData(url, method, entity, retryConfig, count, clazz));
			}
		}
		return retryLogs;
	}
}