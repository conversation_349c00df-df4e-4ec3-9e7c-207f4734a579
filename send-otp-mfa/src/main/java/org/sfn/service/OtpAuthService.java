package org.sfn.service;

import org.sfn.payload.GenericResponse;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Map;

public interface OtpAuthService {
    /**
     * Generate an OTP for the specified user.
     *
     * @param userId          The user ID.
     * @param userName        The username.
     * @param firstName       The user's first name.
     * @param key             The key for the OTP.
     * @param allowedAttempts The maximum number of allowed OTP attempts.
     * @param dataSetMap
     * @return A response indicating success or failure.
     */
    GenericResponse generateOtp(String userId, String userName, String firstName, String key, String allowedAttempts, RedisTemplate<String, Object> redisTemplate, Map<String, String> dataSetMap);

    /**
     * Verify the OTP provided by the user.
     *
     * @param userId    The user ID.
     * @param key       The key for the OTP.
     * @param inputOtp  The OTP provided by the user.
     * @return          A response indicating success or failure.
     */
    GenericResponse verifyOtp(String userId, String key, String inputOtp,RedisTemplate<String, Object> redisTemplate);
}
