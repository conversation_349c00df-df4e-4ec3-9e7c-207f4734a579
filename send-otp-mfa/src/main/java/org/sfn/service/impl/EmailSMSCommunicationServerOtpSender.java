package org.sfn.service.impl;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.sfn.payload.EmailSmsCommunicationDataSetBean;
import org.sfn.payload.EmailSmsCommunicationResponseBean;
import org.sfn.payload.EmailSmsCommunicationRequestBean;
import org.sfn.service.EmailSMSCommunicationServerOtpSenderService;
import org.springframework.beans.factory.annotation.Value;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpEntity;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;


import java.util.Collections;
import java.util.Map;

@Log4j2
@Service
public class EmailSMSCommunicationServerOtpSender implements EmailSMSCommunicationServerOtpSenderService {

    @Value("${send-notification}")
    private String sendNotificationUrl;

    public static final String SUCCESS = "success";
    public static final String COMMUNICATION = "COMMUNICATION";
    private final RestTemplate restTemplate ;
    private final ObjectMapper objectMapper ;

    public EmailSMSCommunicationServerOtpSender(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    @Override
    public void send(String userName, String otp, String firstName, boolean isEmail, Map<String, String> dataSetMap) {
//        var dataSet = new EmailSmsCommunicationDataSetBean(
//                "0", // acquirerId
//                0,   // merchantId
//                isEmail ? "otp_merchant_dashboard_email" : "otp_merchant_dashboard_sms", // eventName
//                firstName, // firstName
//                userName, // Assuming mobileNumber is the same as userName for SMS
//                otp,      // password (assuming this is the OTP)
//                isEmail ? userName : null, // emailTo
//                isEmail ? null : userName, // phoneNumber
//                Result.SUCCESS,
//                SUCCESS,
//                ApiResponseCode.SUCCESS,
//                "00"
//        );
        dataSetMap.put("users__password",otp);
        EmailSmsCommunicationResponseBean responseBean;
        try {
            responseBean = emailSmsCommunicationApiCall(dataSetMap);
            System.out.println("Response for  Email= "+ responseBean.getResult()+" responseCode= "+responseBean.getResponseCode());
        } catch (Exception e) {
           log.error("exception occurred in send email sms in communication service : {} " ,e.getMessage(),e);
        }
    }

    public EmailSmsCommunicationResponseBean emailSmsCommunicationApiCall(Map<String, String> dataSetMap) throws JsonProcessingException {
        EmailSmsCommunicationResponseBean emailSmsResponse;
        try {
            EmailSmsCommunicationRequestBean requestBean = new EmailSmsCommunicationRequestBean();
            requestBean.setSyncService(Collections.singletonList(COMMUNICATION));
            requestBean.setDataSet(dataSetMap);
            System.out.println("requestBean : " + requestBean.getDataSet());

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<EmailSmsCommunicationRequestBean> request = new HttpEntity<>(requestBean, headers);

            String jsonString = objectMapper.writeValueAsString(request);
            System.out.println("URL:"+ sendNotificationUrl);
            System.out.println("Request: "+jsonString);
            ResponseEntity<String> response = restTemplate.exchange(sendNotificationUrl, HttpMethod.POST, request,
                    String.class);
            System.out.println("Response : "+response);
            String responseBody = response.getBody();
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            JsonNode serviceResponsesArray = jsonNode.get("serviceResponses");
            JsonNode firstServiceResponse = serviceResponsesArray.get(0);
            JsonNode serviceResponse = firstServiceResponse.get("serviceResponse");
            emailSmsResponse = objectMapper.treeToValue(serviceResponse,EmailSmsCommunicationResponseBean.class);
        } catch (JsonProcessingException | RestClientException e) {
            log.error("Error making the emailSmsCommunicationApiCall: {}", e.getMessage(), e);
            throw e;
        }
        return emailSmsResponse;
    }

}
