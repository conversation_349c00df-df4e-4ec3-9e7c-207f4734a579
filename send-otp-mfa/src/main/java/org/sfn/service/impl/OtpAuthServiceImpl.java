package org.sfn.service.impl;

import lombok.extern.log4j.Log4j2;
import org.sfn.constants.ApiResponseCode;
import org.sfn.payload.OtpData;
import org.sfn.constants.Result;
import org.sfn.payload.GenericResponse;
import org.sfn.service.EmailSMSCommunicationServerOtpSenderService;
import org.sfn.service.OtpAuthService;
import org.sfn.service.OtpRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Map;

@Log4j2
@Service("otpAuthService")
public class OtpAuthServiceImpl implements OtpAuthService {

    private static final String OTP_PREFIX = "otp:";
    private static final Duration OTP_EXPIRATION = Duration.ofMinutes(5); // OTP expiration time
    private static final Duration ATTEMPTS_EXPIRATION = Duration.ofHours(3); // Attempts expiration time

    private final EmailSMSCommunicationServerOtpSenderService otpSender;
    private final OtpRepository otpRepository;

    @Autowired
    public OtpAuthServiceImpl(EmailSMSCommunicationServerOtpSenderService otpSender, OtpRepository otpRepository) {
        this.otpSender = otpSender;
        this.otpRepository = otpRepository;
    }

    @Override
    public GenericResponse generateOtp(String userId, String userName, String firstName, String key,
                                       String allowedAttempts, RedisTemplate<String, Object> redisTemplate, Map<String, String> dataSetMap) {

        System.out.println(" --------- ENTRY [generateOtp] username : " + userName);
        return generateOtpAndSendEmailSms(userId, userName, firstName, key, allowedAttempts, redisTemplate, dataSetMap);
    }

    @Override
    public GenericResponse verifyOtp(String userId, String key, String inputOtp, RedisTemplate<String, Object> redisTemplate) {
        OtpData otpData = otpRepository.fetchOtpData(userId, key,redisTemplate);
        return verifyOtpWithRedisOtp(userId, key, inputOtp, redisTemplate, otpData);
    }

    private GenericResponse generateOtpAndSendEmailSms(String userId, String userName, String firstName, String key, String allowedAttempts, RedisTemplate<String, Object> redisTemplate, Map<String, String> dataSetMap) {
        OtpData otpData = otpRepository.fetchOtpData(userId, key, redisTemplate);
        System.out.println("existing otp data : " + otpData);

        if (otpData.attempts() >= Integer.parseInt(allowedAttempts)) {
            System.out.println(" --------- EXIT  [generateOtp error for Maximum OTP generation attempts exceeded] : " + userName);
            return new GenericResponse(
                    Result.FAILED,
                    ApiResponseCode.MAX_ATTEMPTS_REACHED,
                    "Maximum OTP generation attempts exceeded"
            );
        }

        String otp = getRandomNumberString();
        System.out.println("OTP generated for user " + userName + " Otp : " + otp);
        boolean isEmail = userName.contains("@");
        // Create a new OTP data object with the current timestamp
        otpData = new OtpData(otp, otpData.attempts() + 1, 3, LocalDateTime.now());

        try {
            otpRepository.saveOtpData(userId, key, otpData, redisTemplate);
            sendOtpUsingCommunicationService(userName, otp, firstName, isEmail, dataSetMap);
        } catch (Exception e) {
            log.error("Exception occurred while generating OTP: {}", e.getMessage(), e);
            System.out.println(" --------- EXIT  [generateOtp error for exception] : " + userName);
            return new GenericResponse(
                    Result.FAILED,
                    ApiResponseCode.FAILED,
                    "Failed to process OTP request."
            );
        }
        System.out.println(" --------- EXIT  [generateOtp success] : " + userName);
        return new GenericResponse(
                Result.SUCCESS,
                ApiResponseCode.SUCCESS,
                "OTP generated successfully"
        );
    }

    public void sendOtpUsingCommunicationService(String userName, String otp, String firstName, boolean isEmail, Map<String, String> dataSetMap) {
        otpSender.send(userName, otp, firstName, isEmail, dataSetMap);
    }

    private String getRandomNumberString() {
        // It will generate 6 digit random Number.
        // from 0 to 999999
        SecureRandom rnd = new SecureRandom();
        int number = rnd.nextInt(999999);

        // this will convert any number sequence into 6 character.
        return "%06d".formatted(number);
    }

    private GenericResponse verifyOtpWithRedisOtp(String userId, String key, String inputOtp, RedisTemplate<String, Object> redisTemplate, OtpData otpData) {
        System.out.println("Time :  " + Instant.now());
        System.out.println("ttt : " + otpData.timestamp().plus(OTP_EXPIRATION));

        if (LocalDateTime.now().isAfter(otpData.timestamp().plus(OTP_EXPIRATION))) {
            otpRepository.deleteOtpData(userId, key, redisTemplate);

            System.out.println("OTP has expired for user: "+ userId);
            return new GenericResponse(
                    Result.FAILED,
                    ApiResponseCode.FAILED,
                    "OTP has expired"
            );
        }

        // Check if number of attempts is exhausted
        if (otpData.validationAttemptsLeft()==0) {
            otpRepository.deleteOtpData(userId, key, redisTemplate);

            System.out.println("OTP validation attempts have been breached for user: "+ userId);
            return new GenericResponse(
                    Result.FAILED,
                    ApiResponseCode.FAILED,
                    "OTP validation attempts breached"
            );
        }

        // Check if OTP is correct
        if (otpData.otp().equals(inputOtp)) {
            System.out.println("OTP verification successful for user: "+ userId);

            // Delete OTP data after successful verification
            otpRepository.deleteOtpData(userId, key, redisTemplate);
            return new GenericResponse(
                    Result.SUCCESS,
                    ApiResponseCode.SUCCESS,
                    "OTP verified successfully"
            );
        } else {
            // Reduce the validationCounter for OTP by 1
            OtpData otpDataNew = new OtpData(otpData.otp(), otpData.attempts(), otpData.validationAttemptsLeft()-1, otpData.timestamp());
            otpRepository.saveOtpData(userId, key, otpDataNew, redisTemplate);

            System.out.println("OTP verification failed for user: "+userId);
            return new GenericResponse(
                    Result.FAILED,
                    ApiResponseCode.FAILED,
                    "Invalid OTP"
            );
        }
    }
}
