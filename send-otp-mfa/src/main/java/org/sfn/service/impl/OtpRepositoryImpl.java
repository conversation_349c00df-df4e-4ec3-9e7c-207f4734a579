package org.sfn.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.sfn.payload.OtpData;
import org.sfn.service.OtpRepository;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Optional;

@Repository
public class OtpRepositoryImpl implements OtpRepository {

    private static final String OTP_PREFIX = "otp:";
    private final ObjectMapper objectMapper;

    public OtpRepositoryImpl(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public OtpData fetchOtpData(String userId, String key,RedisTemplate<String, Object> redisTemplate) {
        String keyStr = OTP_PREFIX.concat(userId).concat(key);
        String otpDataString = Optional.ofNullable((String) redisTemplate.opsForValue().get(keyStr))
                .orElse("{}");

        try {
            return deserializeOtpData(otpDataString);
        } catch (Exception e) {
            throw new RuntimeException("Exception occurred while deserializing OTP data", e);
        }
    }

    @Override
    public void saveOtpData(String userId, String key, OtpData otpData,RedisTemplate<String, Object> redisTemplate) {
        String keyStr = OTP_PREFIX.concat(userId).concat(key);
        try {
            String otpDataString = serializeOtpData(otpData);
            redisTemplate.opsForValue().set(keyStr, otpDataString, Duration.ofHours(3));
        } catch (Exception e) {
            throw new RuntimeException("Exception occurred while serializing OTP data", e);
        }
    }

    @Override
    public void deleteOtpData(String userId, String key,RedisTemplate<String, Object> redisTemplate) {
        String keyStr = OTP_PREFIX.concat(userId).concat(key);
        redisTemplate.delete(keyStr);
    }

    private String serializeOtpData(OtpData otpData) throws Exception {
        var jsonNode = objectMapper.createObjectNode();
        jsonNode.put("otp", otpData.otp());
        jsonNode.put("attempts", otpData.attempts());
        jsonNode.put("timestamp", otpData.timestamp().toString());
        jsonNode.put("validationAttemptsLeft", otpData.validationAttemptsLeft());
        return objectMapper.writeValueAsString(jsonNode);
    }

    private OtpData deserializeOtpData(String otpDataString) throws Exception {
        var jsonNode = (ObjectNode) objectMapper.readTree(otpDataString);
        System.out.println("Deserializing OTP data: "+ jsonNode);
        String otp = jsonNode.has("otp") ? jsonNode.get("otp").asText() : "";
        int attempts = jsonNode.has("attempts") ? jsonNode.get("attempts").asInt() : 0;
        int validationAttemptsLeft = jsonNode.has("validationAttemptsLeft") ? jsonNode.get("validationAttemptsLeft").asInt() : 0;
        LocalDateTime timestamp = jsonNode.has("timestamp") ? LocalDateTime.parse(jsonNode.get("timestamp").asText()) : LocalDateTime.now();
        return new OtpData(otp, attempts, validationAttemptsLeft, timestamp);
    }
}
