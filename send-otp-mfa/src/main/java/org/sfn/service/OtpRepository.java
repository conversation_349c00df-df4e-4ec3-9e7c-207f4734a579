package org.sfn.service;

import org.sfn.payload.OtpData;
import org.springframework.data.redis.core.RedisTemplate;

public interface OtpRepository {
    /**
     * Fetch OTP data for the specified user and key.
     *
     * @param userId  The user ID.
     * @param key     The key for the OTP.
     * @return        The OTP data.
     */
    OtpData fetchOtpData(String userId, String key, RedisTemplate<String,Object> redisTemplate);

    /**
     * Save OTP data for the specified user and key.
     *
     * @param userId    The user ID.
     * @param key       The key for the OTP.
     * @param otpData   The OTP data to save.
     */
    void saveOtpData(String userId, String key, OtpData otpData,RedisTemplate<String,Object> redisTemplate);

    /**
     * Delete OTP data for the specified user and key.
     *
     * @param userId  The user ID.
     * @param key     The key for the OTP.
     */
    void deleteOtpData(String userId, String key,RedisTemplate<String,Object> redisTemplate);
}
