package org.sfn.payload;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class EmailSmsCommunicationDataSetBean {

    @JsonProperty("acquirerId")
    String acquirerId;
    @JsonProperty("merchantId")
    long merchantId;
    @JsonProperty("eventName")
    String eventName;
    @JsonProperty("firstName")
    String firstName;
    @JsonProperty("request__mobileNumber")
    String mobileNumber;
    @JsonProperty("password")
    String password;
    @JsonProperty("request__emailTo")
    String emailTo;
    @JsonProperty("phoneNumber")
    String phoneNumber;
    @JsonProperty("derived__result")
    String result;
    @JsonProperty("derived__responseCode")
    String responseCode;
    @JsonProperty("derived__message")
    String message;
    @JsonProperty("derived__acquirerResponseCode")
    String acquirerResponseCode;

}