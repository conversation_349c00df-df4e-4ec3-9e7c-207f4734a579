package org.sfn.payload;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@ToString
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class EmailSmsCommunicationResponseBean {

    private String result;
    private String responseCode;
    private String responseMessage;
    private String trackingId;
    private String vendorLatency;

}