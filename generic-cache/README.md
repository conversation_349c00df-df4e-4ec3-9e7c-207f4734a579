# Generic Cache Library

A comprehensive generic caching library for Spring Boot applications that supports both `List<T>` and `Map<K, V>` data structures with advanced features like multi-map support, strategy-based refresh mechanisms, and REST API management.

## Features

- **Generic Cache Manager**: Type-safe cache management for any data type
- **Multi-Data Structure Support**: Specialized managers for `List<T>`, `Map<K,V>`, and multi-map scenarios
- **Multi-Map Support**: Handle multiple maps of the same data type for different use cases
- **Strategy Pattern for Refresh**: Pluggable refresh strategies (Scheduled, Manual)
- **REST API Management**: Endpoints for cache refresh, clearing, and monitoring
- **PostConstruct Initialization**: Automatic cache population during application startup
- **Thread-Safe Operations**: Concurrent access support with read-write locks
- **Cache Statistics**: Comprehensive metrics and monitoring
- **Spring Boot Auto-Configuration**: Zero-configuration setup

## Quick Start

### 1. Add Dependency

Add the generic-cache module to your Spring Boot application:

```xml
<dependency>
    <groupId>com.sfn</groupId>
    <artifactId>generic-cache</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. Basic Usage

```java
@Service
@RequiredArgsConstructor
public class UserService {
    
    private final CacheConfigurationHelper cacheHelper;
    private ListCacheManager<User> userCache;
    
    @PostConstruct
    public void initializeCache() {
        userCache = cacheHelper.createListCache(
            "users.all",
            this::loadUsersFromDatabase
        );
    }
    
    public List<User> getAllUsers() {
        return userCache.get().orElse(List.of());
    }
    
    private List<User> loadUsersFromDatabase() throws Exception {
        // Your data loading logic here
        return userRepository.findAll();
    }
}
```

### 3. Configuration

Configure cache behavior in `application.yml`:

```yaml
sfn:
  cache:
    management:
      enabled: true  # Enable REST endpoints
    default-refresh-strategy:
      type: SCHEDULED
      interval-minutes: 60
      initial-delay-minutes: 1
    caches:
      users.all:
        enabled: true
        initialize-on-startup: true
        refresh-strategy:
          type: SCHEDULED
          interval-minutes: 30
```

## Cache Types

### 1. Generic Cache

For any data type:

```java
GenericCacheManager<MyData> cache = cacheHelper.createGenericCache(
    "mydata.cache",
    () -> loadMyData()
);
```

### 2. List Cache

Specialized for `List<T>` with additional operations:

```java
ListCacheManager<Employee> employeeCache = cacheHelper.createListCache(
    "employees.all",
    () -> employeeRepository.findAll()
);

// Additional list operations
int size = employeeCache.size();
boolean contains = employeeCache.contains(employee);
Optional<Employee> first = employeeCache.findFirst(e -> e.getDepartment().equals("IT"));
List<Employee> filtered = employeeCache.filter(e -> e.isActive());
```

### 3. Map Cache

Specialized for `Map<K,V>` with key-based operations:

```java
MapCacheManager<String, Employee> employeeByIdCache = cacheHelper.createMapCache(
    "employees.byId",
    () -> loadEmployeesAsMap()
);

// Map operations
Optional<Employee> employee = employeeByIdCache.get("EMP001");
boolean hasKey = employeeByIdCache.containsKey("EMP001");
Set<String> keys = employeeByIdCache.keySet();
```

### 4. Multi-Map Cache

Handle multiple maps of the same data type:

```java
MultiMapCacheManager multiCache = cacheHelper.createMultiMapCache("employees.multi");

// Register different map views
multiCache.registerMap("byDepartment", this::loadEmployeesByDepartment);
multiCache.registerMap("byLocation", this::loadEmployeesByLocation);
multiCache.registerMap("activeByRole", this::loadActiveEmployeesByRole);

// Access specific maps
Optional<MapCacheManager<String, List<Employee>>> deptCache = 
    multiCache.getMapCache("byDepartment");
```

## Refresh Strategies

### Manual Refresh

```java
// Only refreshes when explicitly called
ManualRefreshStrategy strategy = new ManualRefreshStrategy();
cacheManager.setRefreshStrategy(strategy);
```

### Scheduled Refresh

```java
// Refreshes every 30 minutes with 1-minute initial delay
ScheduledRefreshStrategy strategy = new ScheduledRefreshStrategy(30, 1);
cacheManager.setRefreshStrategy(strategy);
```

## REST API Management

When `sfn.cache.management.enabled=true`, the following endpoints are available:

- `GET /api/cache/names` - Get all cache names
- `GET /api/cache/statistics` - Get statistics for all caches
- `GET /api/cache/statistics/{cacheName}` - Get statistics for specific cache
- `POST /api/cache/refresh/{cacheName}` - Refresh specific cache
- `POST /api/cache/refresh-all` - Refresh all caches
- `DELETE /api/cache/clear/{cacheName}` - Clear specific cache
- `DELETE /api/cache/clear-all` - Clear all caches
- `GET /api/cache/health` - Health check

## Advanced Example

```java
@Service
@RequiredArgsConstructor
public class EmployeeService {
    
    private final CacheConfigurationHelper cacheHelper;
    private final EmployeeRepository employeeRepository;
    
    private ListCacheManager<Employee> allEmployeesCache;
    private MapCacheManager<String, Employee> employeeByIdCache;
    private MultiMapCacheManager employeeMultiMapCache;
    
    @PostConstruct
    public void initializeCaches() {
        // List cache for all employees
        allEmployeesCache = cacheHelper.createListCache(
            "employees.all",
            this::loadAllEmployees
        );
        
        // Map cache for employees by ID
        employeeByIdCache = cacheHelper.createMapCache(
            "employees.byId",
            this::loadEmployeesById
        );
        
        // Multi-map cache for different groupings
        employeeMultiMapCache = cacheHelper.createMultiMapCache("employees.multiMap");
        employeeMultiMapCache.registerMap("byDepartment", this::loadEmployeesByDepartment);
        employeeMultiMapCache.registerMap("byLocation", this::loadEmployeesByLocation);
    }
    
    public List<Employee> getAllEmployees() {
        return allEmployeesCache.get().orElse(List.of());
    }
    
    public Optional<Employee> getEmployeeById(String id) {
        return employeeByIdCache.get(id);
    }
    
    public List<Employee> getEmployeesByDepartment(String department) {
        return employeeMultiMapCache.<String, List<Employee>>getMapCache("byDepartment")
            .flatMap(cache -> cache.get(department))
            .orElse(List.of());
    }
    
    // Data loading methods
    private List<Employee> loadAllEmployees() throws Exception {
        return employeeRepository.findAll();
    }
    
    private Map<String, Employee> loadEmployeesById() throws Exception {
        return employeeRepository.findAll().stream()
            .collect(Collectors.toMap(Employee::getId, e -> e));
    }
    
    private Map<String, List<Employee>> loadEmployeesByDepartment() throws Exception {
        return employeeRepository.findAll().stream()
            .collect(Collectors.groupingBy(Employee::getDepartment));
    }
}
```

## Configuration Properties

| Property | Default | Description |
|----------|---------|-------------|
| `sfn.cache.management.enabled` | `false` | Enable REST management endpoints |
| `sfn.cache.management.base-path` | `/api/cache` | Base path for management endpoints |
| `sfn.cache.default-refresh-strategy.type` | `MANUAL` | Default refresh strategy type |
| `sfn.cache.default-refresh-strategy.interval-minutes` | `60` | Default refresh interval |
| `sfn.cache.default-refresh-strategy.initial-delay-minutes` | `1` | Default initial delay |

## Thread Safety

All cache managers are thread-safe and use read-write locks to ensure:
- Multiple concurrent reads
- Exclusive writes
- Consistent data during refresh operations

## Monitoring and Statistics

Each cache provides comprehensive statistics:
- Hit/miss counts and ratios
- Refresh counts and timestamps
- Data size and type information
- Creation and last refresh times

## Best Practices

1. **Use @PostConstruct** for cache initialization
2. **Choose appropriate refresh strategies** based on data volatility
3. **Monitor cache statistics** to optimize performance
4. **Use multi-map caches** for complex data relationships
5. **Configure proper refresh intervals** to balance freshness and performance
6. **Handle exceptions** in data loading methods gracefully

## License

This library is part of the SFN Common Modules project.
