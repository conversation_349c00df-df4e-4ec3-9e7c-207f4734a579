# Example configuration for Generic Cache Library
# Copy this to your application.yml and customize as needed

sfn:
  cache:
    # Enable cache management REST endpoints
    management:
      enabled: true
      base-path: /api/cache
    
    # Default refresh strategy for all caches
    default-refresh-strategy:
      type: MANUAL  # Options: MANUAL, SCHEDULED
      interval-minutes: 60
      initial-delay-minutes: 1
    
    # Cache-specific configurations
    caches:
      # Employee caches
      employees.all:
        enabled: true
        initialize-on-startup: true
        refresh-strategy:
          type: SCHEDULED
          interval-minutes: 30
          initial-delay-minutes: 2
      
      employees.byId:
        enabled: true
        initialize-on-startup: true
        refresh-strategy:
          type: SCHEDULED
          interval-minutes: 30
          initial-delay-minutes: 3
      
      employees.multiMap:
        enabled: true
        initialize-on-startup: true
        refresh-strategy:
          type: SCHEDULED
          interval-minutes: 45
          initial-delay-minutes: 5
      
      # User caches
      users.all:
        enabled: true
        initialize-on-startup: false  # Manual initialization
        refresh-strategy:
          type: MANUAL
      
      # Product caches with frequent updates
      products.active:
        enabled: true
        initialize-on-startup: true
        refresh-strategy:
          type: SCHEDULED
          interval-minutes: 15  # Refresh every 15 minutes
          initial-delay-minutes: 1
      
      # Configuration cache with infrequent updates
      config.settings:
        enabled: true
        initialize-on-startup: true
        refresh-strategy:
          type: SCHEDULED
          interval-minutes: 120  # Refresh every 2 hours
          initial-delay-minutes: 1

# Logging configuration for cache operations
logging:
  level:
    com.sfn.cache: INFO
    com.sfn.cache.impl: DEBUG  # Enable for detailed cache operations
    com.sfn.cache.strategy: INFO
    com.sfn.cache.controller: INFO

# Management endpoints (if using Spring Boot Actuator)
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
