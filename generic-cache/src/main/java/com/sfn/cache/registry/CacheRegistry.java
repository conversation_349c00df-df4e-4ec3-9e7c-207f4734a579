package com.sfn.cache.registry;

import com.sfn.cache.CacheManager;
import com.sfn.cache.CacheStatistics;
import com.sfn.cache.impl.GenericCacheManager;
import com.sfn.cache.impl.MultiMapCacheManager;
import jakarta.annotation.PreDestroy;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Registry to manage all cache instances in the application.
 * Provides centralized access to all caches and their operations.
 */
@Log4j2
@Component
public class CacheRegistry {
    
    private final Map<String, CacheManager<?>> caches = new ConcurrentHashMap<>();
    
    /**
     * Registers a cache manager with the registry.
     * 
     * @param cacheManager The cache manager to register
     * @param <T> The type of data stored in the cache
     */
    public <T> void registerCache(CacheManager<T> cacheManager) {
        String cacheName = cacheManager.getCacheName();
        if (caches.containsKey(cacheName)) {
            log.warn("Cache with name '{}' already exists. Replacing with new instance.", cacheName);
        }
        
        caches.put(cacheName, cacheManager);
        log.info("Registered cache: {}", cacheName);
    }
    
    /**
     * Gets a cache manager by name.
     * 
     * @param cacheName The cache name
     * @param <T> The type of data stored in the cache
     * @return Optional containing the cache manager, or empty if not found
     */
    @SuppressWarnings("unchecked")
    public <T> Optional<CacheManager<T>> getCache(String cacheName) {
        CacheManager<?> cache = caches.get(cacheName);
        return cache != null ? Optional.of((CacheManager<T>) cache) : Optional.empty();
    }
    
    /**
     * Unregisters a cache from the registry.
     * 
     * @param cacheName The cache name to unregister
     * @return true if cache was removed, false if it didn't exist
     */
    public boolean unregisterCache(String cacheName) {
        CacheManager<?> removed = caches.remove(cacheName);
        if (removed != null) {
            // Stop refresh strategies if applicable
            if (removed instanceof GenericCacheManager) {
                ((GenericCacheManager<?>) removed).stopRefreshStrategy();
            } else if (removed instanceof MultiMapCacheManager) {
                ((MultiMapCacheManager) removed).stopRefreshStrategy();
            }
            log.info("Unregistered cache: {}", cacheName);
            return true;
        }
        return false;
    }
    
    /**
     * Gets all registered cache names.
     * 
     * @return Collection of cache names
     */
    public Collection<String> getCacheNames() {
        return caches.keySet();
    }
    
    /**
     * Gets all registered cache managers.
     * 
     * @return Collection of cache managers
     */
    public Collection<CacheManager<?>> getAllCaches() {
        return caches.values();
    }
    
    /**
     * Refreshes a specific cache by name.
     * 
     * @param cacheName The cache name to refresh
     * @return true if cache was found and refreshed, false otherwise
     */
    public boolean refreshCache(String cacheName) {
        CacheManager<?> cache = caches.get(cacheName);
        if (cache != null) {
            try {
                cache.refresh();
                log.info("Refreshed cache: {}", cacheName);
                return true;
            } catch (Exception e) {
                log.error("Failed to refresh cache: {}", cacheName, e);
                throw e;
            }
        }
        log.warn("Cache not found for refresh: {}", cacheName);
        return false;
    }
    
    /**
     * Refreshes all registered caches.
     * 
     * @return Number of caches successfully refreshed
     */
    public int refreshAllCaches() {
        log.info("Refreshing all caches...");
        int successCount = 0;
        
        for (Map.Entry<String, CacheManager<?>> entry : caches.entrySet()) {
            try {
                entry.getValue().refresh();
                successCount++;
                log.debug("Refreshed cache: {}", entry.getKey());
            } catch (Exception e) {
                log.error("Failed to refresh cache: {}", entry.getKey(), e);
            }
        }
        
        log.info("Refreshed {}/{} caches successfully", successCount, caches.size());
        return successCount;
    }
    
    /**
     * Clears a specific cache by name.
     * 
     * @param cacheName The cache name to clear
     * @return true if cache was found and cleared, false otherwise
     */
    public boolean clearCache(String cacheName) {
        CacheManager<?> cache = caches.get(cacheName);
        if (cache != null) {
            cache.clear();
            log.info("Cleared cache: {}", cacheName);
            return true;
        }
        log.warn("Cache not found for clear: {}", cacheName);
        return false;
    }
    
    /**
     * Clears all registered caches.
     */
    public void clearAllCaches() {
        log.info("Clearing all caches...");
        caches.values().forEach(CacheManager::clear);
        log.info("Cleared all {} caches", caches.size());
    }
    
    /**
     * Gets statistics for a specific cache.
     * 
     * @param cacheName The cache name
     * @return Optional containing cache statistics, or empty if cache not found
     */
    public Optional<CacheStatistics> getCacheStatistics(String cacheName) {
        CacheManager<?> cache = caches.get(cacheName);
        return cache != null ? Optional.of(cache.getStatistics()) : Optional.empty();
    }
    
    /**
     * Gets statistics for all caches.
     * 
     * @return Map of cache name to statistics
     */
    public Map<String, CacheStatistics> getAllCacheStatistics() {
        Map<String, CacheStatistics> stats = new java.util.HashMap<>();
        caches.forEach((name, cache) -> stats.put(name, cache.getStatistics()));
        return stats;
    }
    
    /**
     * Checks if a cache exists.
     * 
     * @param cacheName The cache name to check
     * @return true if cache exists, false otherwise
     */
    public boolean cacheExists(String cacheName) {
        return caches.containsKey(cacheName);
    }
    
    /**
     * Gets the total number of registered caches.
     * 
     * @return The number of caches
     */
    public int getCacheCount() {
        return caches.size();
    }
    
    /**
     * Cleanup method called when the application shuts down.
     */
    @PreDestroy
    public void shutdown() {
        log.info("Shutting down cache registry...");
        
        // Stop all refresh strategies
        caches.values().forEach(cache -> {
            try {
                if (cache instanceof GenericCacheManager) {
                    ((GenericCacheManager<?>) cache).stopRefreshStrategy();
                } else if (cache instanceof MultiMapCacheManager) {
                    ((MultiMapCacheManager) cache).stopRefreshStrategy();
                }
            } catch (Exception e) {
                log.error("Error stopping refresh strategy for cache: {}", cache.getCacheName(), e);
            }
        });
        
        caches.clear();
        log.info("Cache registry shutdown complete");
    }
}
