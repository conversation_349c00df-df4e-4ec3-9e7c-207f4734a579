package com.sfn.cache;

/**
 * Functional interface for loading cache data.
 * This interface defines the contract for loading data into cache.
 * 
 * @param <T> The type of data to be loaded
 */
@FunctionalInterface
public interface CacheDataLoader<T> {
    
    /**
     * Loads data for the cache.
     * 
     * @return The loaded data
     * @throws Exception if data loading fails
     */
    T loadData() throws Exception;
}
