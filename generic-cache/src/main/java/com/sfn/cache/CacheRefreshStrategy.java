package com.sfn.cache;

/**
 * Strategy interface for cache refresh mechanisms.
 * Implementations define different ways to refresh cache data.
 */
public interface CacheRefreshStrategy {
    
    /**
     * Initializes the refresh strategy for a specific cache.
     * 
     * @param cacheName The name of the cache
     * @param refreshCallback The callback to execute when refresh is needed
     */
    void initialize(String cacheName, Runnable refreshCallback);
    
    /**
     * Starts the refresh mechanism.
     */
    void start();
    
    /**
     * Stops the refresh mechanism.
     */
    void stop();
    
    /**
     * Triggers an immediate refresh.
     */
    void triggerRefresh();
    
    /**
     * Gets the strategy name.
     * 
     * @return The strategy name
     */
    String getStrategyName();
}
