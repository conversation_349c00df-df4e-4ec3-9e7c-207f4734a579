package com.sfn.cache.impl;

import com.sfn.cache.CacheDataLoader;
import com.sfn.cache.CacheManager;
import com.sfn.cache.CacheRefreshStrategy;
import com.sfn.cache.CacheStatistics;
import lombok.extern.log4j.Log4j2;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Multi-map cache manager that can handle multiple maps of the same data type
 * but for different use cases within a single service method.
 * 
 * Example: Employee data as both Map<String, Employee> (keyed by ID) and 
 * Map<Department, List<Employee>> (grouped by department).
 */
@Log4j2
public class MultiMapCacheManager implements CacheManager<Map<String, Object>> {
    
    private final String cacheName;
    private final Map<String, MapCacheManager<?, ?>> mapCaches = new ConcurrentHashMap<>();
    private final Map<String, CacheDataLoader<?>> dataLoaders = new ConcurrentHashMap<>();
    private volatile LocalDateTime lastRefreshTime;
    private CacheRefreshStrategy refreshStrategy;
    
    public MultiMapCacheManager(String cacheName) {
        this.cacheName = cacheName;
        log.info("Created multi-map cache manager for: {}", cacheName);
    }
    
    /**
     * Registers a map cache with a specific key and data loader.
     * 
     * @param mapKey The key to identify this map
     * @param dataLoader The data loader for this map
     * @param <K> The key type of the map
     * @param <V> The value type of the map
     */
    public <K, V> void registerMap(String mapKey, CacheDataLoader<Map<K, V>> dataLoader) {
        MapCacheManager<K, V> mapCache = new MapCacheManager<>(cacheName + "." + mapKey);
        mapCache.setDataLoader(dataLoader);
        mapCaches.put(mapKey, mapCache);
        dataLoaders.put(mapKey, dataLoader);
        log.info("Registered map cache: {} for multi-map: {}", mapKey, cacheName);
    }
    
    /**
     * Gets a specific map cache by key.
     * 
     * @param mapKey The map key
     * @param <K> The key type of the map
     * @param <V> The value type of the map
     * @return Optional containing the map cache, or empty if not found
     */
    @SuppressWarnings("unchecked")
    public <K, V> Optional<MapCacheManager<K, V>> getMapCache(String mapKey) {
        MapCacheManager<?, ?> cache = mapCaches.get(mapKey);
        return cache != null ? Optional.of((MapCacheManager<K, V>) cache) : Optional.empty();
    }
    
    /**
     * Gets data from a specific map by map key and data key.
     * 
     * @param mapKey The map identifier
     * @param dataKey The key within the map
     * @param <K> The key type of the map
     * @param <V> The value type of the map
     * @return Optional containing the value, or empty if not found
     */
    @SuppressWarnings("unchecked")
    public <K, V> Optional<V> get(String mapKey, K dataKey) {
        MapCacheManager<K, V> mapCache = (MapCacheManager<K, V>) mapCaches.get(mapKey);
        return mapCache != null ? mapCache.get(dataKey) : Optional.empty();
    }
    
    /**
     * Gets all registered map keys.
     * 
     * @return Set of map keys
     */
    public java.util.Set<String> getMapKeys() {
        return mapCaches.keySet();
    }
    
    @Override
    public Optional<Map<String, Object>> get() {
        Map<String, Object> allMaps = new java.util.HashMap<>();
        mapCaches.forEach((key, cache) -> {
            cache.get().ifPresent(data -> allMaps.put(key, data));
        });
        return allMaps.isEmpty() ? Optional.empty() : Optional.of(allMaps);
    }
    
    @Override
    public void put(Map<String, Object> data) {
        // This method is not typically used for MultiMapCacheManager
        // Individual maps are populated through their specific data loaders
        log.warn("Direct put operation not supported for MultiMapCacheManager. Use registerMap and refresh instead.");
    }
    
    @Override
    public void clear() {
        mapCaches.values().forEach(MapCacheManager::clear);
        lastRefreshTime = null;
        log.info("Cleared all map caches for: {}", cacheName);
    }
    
    @Override
    public void refresh() {
        log.info("Refreshing all map caches for: {}", cacheName);
        mapCaches.values().forEach(MapCacheManager::refresh);
        lastRefreshTime = LocalDateTime.now();
        log.info("Refreshed all map caches for: {}", cacheName);
    }
    
    /**
     * Refreshes a specific map cache.
     * 
     * @param mapKey The map key to refresh
     */
    public void refresh(String mapKey) {
        MapCacheManager<?, ?> cache = mapCaches.get(mapKey);
        if (cache != null) {
            cache.refresh();
            log.info("Refreshed map cache: {} for multi-map: {}", mapKey, cacheName);
        } else {
            log.warn("Map cache not found: {} for multi-map: {}", mapKey, cacheName);
        }
    }
    
    @Override
    public boolean isEmpty() {
        return mapCaches.values().stream().allMatch(MapCacheManager::isEmpty);
    }
    
    @Override
    public String getCacheName() {
        return cacheName;
    }
    
    @Override
    public LocalDateTime getLastRefreshTime() {
        return lastRefreshTime;
    }
    
    @Override
    public CacheStatistics getStatistics() {
        // Aggregate statistics from all map caches
        long totalHits = mapCaches.values().stream()
                .mapToLong(cache -> cache.getStatistics().getHitCount())
                .sum();
        long totalMisses = mapCaches.values().stream()
                .mapToLong(cache -> cache.getStatistics().getMissCount())
                .sum();
        long totalRefreshes = mapCaches.values().stream()
                .mapToLong(cache -> cache.getStatistics().getRefreshCount())
                .sum();
        long totalSize = mapCaches.values().stream()
                .mapToLong(cache -> cache.getStatistics().getDataSize())
                .sum();
        
        return CacheStatistics.builder()
                .cacheName(cacheName)
                .hitCount(totalHits)
                .missCount(totalMisses)
                .refreshCount(totalRefreshes)
                .lastRefreshTime(lastRefreshTime)
                .dataSize(totalSize)
                .dataType("MultiMap")
                .build();
    }
    
    @Override
    public void setDataLoader(CacheDataLoader<Map<String, Object>> dataLoader) {
        // Not applicable for MultiMapCacheManager
        log.warn("setDataLoader not supported for MultiMapCacheManager. Use registerMap instead.");
    }
    
    @Override
    public void setRefreshStrategy(CacheRefreshStrategy refreshStrategy) {
        this.refreshStrategy = refreshStrategy;
        if (refreshStrategy != null) {
            refreshStrategy.initialize(cacheName, this::refresh);
            // Also set refresh strategy for individual map caches
            mapCaches.values().forEach(cache -> {
                if (cache instanceof GenericCacheManager) {
                    ((GenericCacheManager<?>) cache).setRefreshStrategy(refreshStrategy);
                }
            });
        }
    }
    
    /**
     * Starts the refresh strategy for all map caches.
     */
    public void startRefreshStrategy() {
        if (refreshStrategy != null) {
            refreshStrategy.start();
        }
        mapCaches.values().forEach(cache -> {
            if (cache instanceof GenericCacheManager) {
                ((GenericCacheManager<?>) cache).startRefreshStrategy();
            }
        });
        log.info("Started refresh strategies for multi-map cache: {}", cacheName);
    }
    
    /**
     * Stops the refresh strategy for all map caches.
     */
    public void stopRefreshStrategy() {
        if (refreshStrategy != null) {
            refreshStrategy.stop();
        }
        mapCaches.values().forEach(cache -> {
            if (cache instanceof GenericCacheManager) {
                ((GenericCacheManager<?>) cache).stopRefreshStrategy();
            }
        });
        log.info("Stopped refresh strategies for multi-map cache: {}", cacheName);
    }
}
