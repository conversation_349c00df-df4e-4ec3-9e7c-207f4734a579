package com.sfn.cache.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * Configuration properties for the generic cache library.
 */
@Data
@ConfigurationProperties(prefix = "sfn.cache")
public class CacheProperties {
    
    /**
     * Whether cache management is enabled.
     */
    private Management management = new Management();
    
    /**
     * Default refresh strategy configuration.
     */
    private RefreshStrategy defaultRefreshStrategy = new RefreshStrategy();
    
    /**
     * Cache-specific configurations.
     */
    private Map<String, CacheConfig> caches = new HashMap<>();
    
    /**
     * Management configuration.
     */
    @Data
    public static class Management {
        /**
         * Whether cache management endpoints are enabled.
         */
        private boolean enabled = false;
        
        /**
         * Base path for cache management endpoints.
         */
        private String basePath = "/api/cache";
    }
    
    /**
     * Refresh strategy configuration.
     */
    @Data
    public static class RefreshStrategy {
        /**
         * Type of refresh strategy (SCHEDULED, MANUAL).
         */
        private StrategyType type = StrategyType.MANUAL;
        
        /**
         * Refresh interval in minutes (for SCHEDULED strategy).
         */
        private long intervalMinutes = 60;
        
        /**
         * Initial delay in minutes (for SCHEDULED strategy).
         */
        private long initialDelayMinutes = 1;
    }
    
    /**
     * Cache-specific configuration.
     */
    @Data
    public static class CacheConfig {
        /**
         * Whether this cache is enabled.
         */
        private boolean enabled = true;
        
        /**
         * Refresh strategy for this cache.
         */
        private RefreshStrategy refreshStrategy = new RefreshStrategy();
        
        /**
         * Whether to initialize cache on startup.
         */
        private boolean initializeOnStartup = true;
    }
    
    /**
     * Refresh strategy types.
     */
    public enum StrategyType {
        SCHEDULED,
        MANUAL
    }
    
    /**
     * Gets configuration for a specific cache.
     * 
     * @param cacheName The cache name
     * @return Cache configuration, or default if not specified
     */
    public CacheConfig getCacheConfig(String cacheName) {
        return caches.getOrDefault(cacheName, new CacheConfig());
    }
    
    /**
     * Sets configuration for a specific cache.
     * 
     * @param cacheName The cache name
     * @param config The cache configuration
     */
    public void setCacheConfig(String cacheName, CacheConfig config) {
        caches.put(cacheName, config);
    }
}
