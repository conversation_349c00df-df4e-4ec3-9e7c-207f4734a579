package com.sfn.cache;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Cache statistics data class.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CacheStatistics {
    
    private String cacheName;
    private long hitCount;
    private long missCount;
    private long refreshCount;
    private LocalDateTime lastRefreshTime;
    private LocalDateTime creationTime;
    private long dataSize;
    private String dataType;
    
    /**
     * Gets the hit ratio.
     * 
     * @return Hit ratio as a percentage
     */
    public double getHitRatio() {
        long totalRequests = hitCount + missCount;
        return totalRequests == 0 ? 0.0 : (double) hitCount / totalRequests * 100;
    }
    
    /**
     * Gets the total request count.
     * 
     * @return Total request count
     */
    public long getTotalRequests() {
        return hitCount + missCount;
    }
}
