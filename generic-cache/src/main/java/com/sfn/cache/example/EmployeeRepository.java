package com.sfn.cache.example;

import java.util.List;

/**
 * Example repository interface for Employee data.
 * In a real application, this would be implemented with JPA, MyBatis, or other data access technology.
 */
public interface EmployeeRepository {
    
    /**
     * Finds all employees.
     * 
     * @return List of all employees
     */
    List<Employee> findAll();
    
    /**
     * Finds all active employees.
     * 
     * @return List of active employees
     */
    List<Employee> findByActiveTrue();
    
    /**
     * Finds employees by department.
     * 
     * @param department The department
     * @return List of employees in the department
     */
    List<Employee> findByDepartment(String department);
    
    /**
     * Finds employees by location.
     * 
     * @param location The location
     * @return List of employees at the location
     */
    List<Employee> findByLocation(String location);
    
    /**
     * Finds an employee by ID.
     * 
     * @param id The employee ID
     * @return The employee, or null if not found
     */
    Employee findById(String id);
}
