package com.sfn.cache.example;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * Example Employee entity for demonstrating cache usage.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Employee {
    
    private String id;
    private String name;
    private String email;
    private String department;
    private String role;
    private String location;
    private boolean active;
    private LocalDate hireDate;
    private Double salary;
}
