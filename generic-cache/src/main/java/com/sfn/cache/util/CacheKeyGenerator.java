package com.sfn.cache.util;

import org.apache.commons.lang3.StringUtils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * Utility class for generating cache keys.
 */
public class CacheKeyGenerator {
    
    private static final String DEFAULT_SEPARATOR = ":";
    
    /**
     * Generates a cache key from multiple components.
     * 
     * @param components The components to combine
     * @return The generated cache key
     */
    public static String generateKey(Object... components) {
        return generateKey(DEFAULT_SEPARATOR, components);
    }
    
    /**
     * Generates a cache key from multiple components with a custom separator.
     * 
     * @param separator The separator to use
     * @param components The components to combine
     * @return The generated cache key
     */
    public static String generateKey(String separator, Object... components) {
        if (components == null || components.length == 0) {
            throw new IllegalArgumentException("At least one component is required");
        }
        
        return Arrays.stream(components)
                .map(component -> component != null ? component.toString() : "null")
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(separator));
    }
    
    /**
     * Generates a hashed cache key from multiple components.
     * Useful when the key might be too long or contain special characters.
     * 
     * @param components The components to combine and hash
     * @return The hashed cache key
     */
    public static String generateHashedKey(Object... components) {
        String key = generateKey(components);
        return hashKey(key);
    }
    
    /**
     * Generates a prefixed cache key.
     * 
     * @param prefix The prefix
     * @param components The components to combine
     * @return The prefixed cache key
     */
    public static String generatePrefixedKey(String prefix, Object... components) {
        String key = generateKey(components);
        return StringUtils.isNotBlank(prefix) ? prefix + DEFAULT_SEPARATOR + key : key;
    }
    
    /**
     * Hashes a key using SHA-256.
     * 
     * @param key The key to hash
     * @return The hashed key
     */
    public static String hashKey(String key) {
        if (StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("Key cannot be blank");
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(key.getBytes());
            StringBuilder hexString = new StringBuilder();
            
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not available", e);
        }
    }
    
    /**
     * Validates a cache key.
     * 
     * @param key The key to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidKey(String key) {
        return StringUtils.isNotBlank(key) && !key.contains(" ") && key.length() <= 250;
    }
    
    /**
     * Sanitizes a cache key by removing invalid characters.
     * 
     * @param key The key to sanitize
     * @return The sanitized key
     */
    public static String sanitizeKey(String key) {
        if (StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("Key cannot be blank");
        }
        
        // Replace spaces and special characters with underscores
        String sanitized = key.replaceAll("[\\s\\-\\.]", "_")
                             .replaceAll("[^a-zA-Z0-9_:]", "");
        
        // Truncate if too long
        if (sanitized.length() > 250) {
            sanitized = sanitized.substring(0, 200) + "_" + hashKey(key).substring(0, 8);
        }
        
        return sanitized;
    }
}
