package com.sfn.cache;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * Generic cache manager interface that provides basic cache operations.
 * 
 * @param <T> The type of data stored in the cache
 */
public interface CacheManager<T> {
    
    /**
     * Gets the cached data.
     * 
     * @return Optional containing the cached data, or empty if not available
     */
    Optional<T> get();
    
    /**
     * Puts data into the cache.
     * 
     * @param data The data to cache
     */
    void put(T data);
    
    /**
     * Clears the cache.
     */
    void clear();
    
    /**
     * Refreshes the cache by reloading data.
     */
    void refresh();
    
    /**
     * Checks if the cache is empty.
     * 
     * @return true if cache is empty, false otherwise
     */
    boolean isEmpty();
    
    /**
     * Gets the cache name.
     * 
     * @return The cache name
     */
    String getCacheName();
    
    /**
     * Gets the last refresh time.
     * 
     * @return The last refresh time, or null if never refreshed
     */
    LocalDateTime getLastRefreshTime();
    
    /**
     * Gets cache statistics.
     * 
     * @return Cache statistics
     */
    CacheStatistics getStatistics();
    
    /**
     * Sets the data loader for this cache.
     * 
     * @param dataLoader The data loader
     */
    void setDataLoader(CacheDataLoader<T> dataLoader);
    
    /**
     * Sets the refresh strategy for this cache.
     * 
     * @param refreshStrategy The refresh strategy
     */
    void setRefreshStrategy(CacheRefreshStrategy refreshStrategy);
}
