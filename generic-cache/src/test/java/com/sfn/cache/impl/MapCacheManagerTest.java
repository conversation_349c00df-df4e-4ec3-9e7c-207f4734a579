package com.sfn.cache.impl;

import com.sfn.cache.CacheDataLoader;
import com.sfn.cache.strategy.ManualRefreshStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for MapCacheManager.
 */
class MapCacheManagerTest {
    
    private MapCacheManager<String, String> cacheManager;
    private CacheDataLoader<Map<String, String>> dataLoader;
    
    @BeforeEach
    void setUp() {
        cacheManager = new MapCacheManager<>("test-map-cache");
        dataLoader = () -> Map.of(
            "key1", "value1",
            "key2", "value2",
            "key3", "value3"
        );
        cacheManager.setDataLoader(dataLoader);
        cacheManager.setRefreshStrategy(new ManualRefreshStrategy());
        cacheManager.refresh(); // Load initial data
    }
    
    @Test
    void testGetByKey() {
        assertEquals(Optional.of("value1"), cacheManager.get("key1"));
        assertEquals(Optional.of("value2"), cacheManager.get("key2"));
        assertEquals(Optional.empty(), cacheManager.get("nonexistent"));
    }
    
    @Test
    void testContainsKey() {
        assertTrue(cacheManager.containsKey("key1"));
        assertTrue(cacheManager.containsKey("key2"));
        assertFalse(cacheManager.containsKey("nonexistent"));
    }
    
    @Test
    void testContainsValue() {
        assertTrue(cacheManager.containsValue("value1"));
        assertTrue(cacheManager.containsValue("value2"));
        assertFalse(cacheManager.containsValue("nonexistent"));
    }
    
    @Test
    void testKeySet() {
        Set<String> keys = cacheManager.keySet();
        assertEquals(Set.of("key1", "key2", "key3"), keys);
    }
    
    @Test
    void testValues() {
        var values = cacheManager.values();
        assertEquals(3, values.size());
        assertTrue(values.contains("value1"));
        assertTrue(values.contains("value2"));
        assertTrue(values.contains("value3"));
    }
    
    @Test
    void testSize() {
        assertEquals(3, cacheManager.size());
        
        cacheManager.clear();
        assertEquals(0, cacheManager.size());
    }
    
    @Test
    void testEntrySet() {
        var entries = cacheManager.entrySet();
        assertEquals(3, entries.size());
    }
    
    @Test
    void testFilter() {
        Map<String, String> filtered = cacheManager.filter(
            entry -> entry.getKey().equals("key1") || entry.getKey().equals("key3")
        );
        
        assertEquals(2, filtered.size());
        assertEquals("value1", filtered.get("key1"));
        assertEquals("value3", filtered.get("key3"));
        assertNull(filtered.get("key2"));
    }
    
    @Test
    void testCreateWithDataLoader() {
        MapCacheManager<Integer, String> numberCache = MapCacheManager.create(
            "numbers",
            () -> Map.of(1, "one", 2, "two", 3, "three")
        );
        
        numberCache.refresh();
        assertEquals(3, numberCache.size());
        assertEquals(Optional.of("two"), numberCache.get(2));
    }
}
