package com.sfn.cache.impl;

import com.sfn.cache.CacheDataLoader;
import com.sfn.cache.strategy.ManualRefreshStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ListCacheManager.
 */
class ListCacheManagerTest {
    
    private ListCacheManager<String> cacheManager;
    private CacheDataLoader<List<String>> dataLoader;
    
    @BeforeEach
    void setUp() {
        cacheManager = new ListCacheManager<>("test-list-cache");
        dataLoader = () -> List.of("apple", "banana", "cherry", "date");
        cacheManager.setDataLoader(dataLoader);
        cacheManager.setRefreshStrategy(new ManualRefreshStrategy());
        cacheManager.refresh(); // Load initial data
    }
    
    @Test
    void testSize() {
        assertEquals(4, cacheManager.size());
        
        cacheManager.clear();
        assertEquals(0, cacheManager.size());
    }
    
    @Test
    void testContains() {
        assertTrue(cacheManager.contains("apple"));
        assertTrue(cacheManager.contains("banana"));
        assertFalse(cacheManager.contains("grape"));
    }
    
    @Test
    void testGetByIndex() {
        assertEquals(Optional.of("apple"), cacheManager.get(0));
        assertEquals(Optional.of("banana"), cacheManager.get(1));
        assertEquals(Optional.of("cherry"), cacheManager.get(2));
        assertEquals(Optional.of("date"), cacheManager.get(3));
        
        assertEquals(Optional.empty(), cacheManager.get(4));
        assertEquals(Optional.empty(), cacheManager.get(-1));
    }
    
    @Test
    void testFindFirst() {
        Optional<String> result = cacheManager.findFirst(s -> s.startsWith("c"));
        assertEquals(Optional.of("cherry"), result);
        
        Optional<String> notFound = cacheManager.findFirst(s -> s.startsWith("z"));
        assertEquals(Optional.empty(), notFound);
    }
    
    @Test
    void testFilter() {
        List<String> filtered = cacheManager.filter(s -> s.length() > 5);
        assertEquals(List.of("banana", "cherry"), filtered);
        
        List<String> empty = cacheManager.filter(s -> s.startsWith("z"));
        assertTrue(empty.isEmpty());
    }
    
    @Test
    void testCreateWithDataLoader() {
        ListCacheManager<Integer> numberCache = ListCacheManager.create(
            "numbers",
            () -> List.of(1, 2, 3, 4, 5)
        );
        
        numberCache.refresh();
        assertEquals(5, numberCache.size());
        assertTrue(numberCache.contains(3));
    }
}
