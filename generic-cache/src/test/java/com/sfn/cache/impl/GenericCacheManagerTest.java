package com.sfn.cache.impl;

import com.sfn.cache.CacheDataLoader;
import com.sfn.cache.strategy.ManualRefreshStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for GenericCacheManager.
 */
class GenericCacheManagerTest {
    
    private GenericCacheManager<List<String>> cacheManager;
    private CacheDataLoader<List<String>> dataLoader;
    
    @BeforeEach
    void setUp() {
        cacheManager = new GenericCacheManager<>("test-cache");
        dataLoader = () -> List.of("item1", "item2", "item3");
        cacheManager.setDataLoader(dataLoader);
        cacheManager.setRefreshStrategy(new ManualRefreshStrategy());
    }
    
    @Test
    void testCacheInitiallyEmpty() {
        assertTrue(cacheManager.isEmpty());
        assertEquals(Optional.empty(), cacheManager.get());
    }
    
    @Test
    void testPutAndGet() {
        List<String> testData = List.of("test1", "test2");
        cacheManager.put(testData);
        
        assertFalse(cacheManager.isEmpty());
        Optional<List<String>> cached = cacheManager.get();
        assertTrue(cached.isPresent());
        assertEquals(testData, cached.get());
    }
    
    @Test
    void testRefresh() {
        cacheManager.refresh();
        
        assertFalse(cacheManager.isEmpty());
        Optional<List<String>> cached = cacheManager.get();
        assertTrue(cached.isPresent());
        assertEquals(List.of("item1", "item2", "item3"), cached.get());
    }
    
    @Test
    void testClear() {
        cacheManager.refresh();
        assertFalse(cacheManager.isEmpty());
        
        cacheManager.clear();
        assertTrue(cacheManager.isEmpty());
        assertEquals(Optional.empty(), cacheManager.get());
    }
    
    @Test
    void testCacheName() {
        assertEquals("test-cache", cacheManager.getCacheName());
    }
    
    @Test
    void testStatistics() {
        // Initial state
        assertEquals(0, cacheManager.getStatistics().getHitCount());
        assertEquals(0, cacheManager.getStatistics().getMissCount());
        
        // Miss
        cacheManager.get();
        assertEquals(0, cacheManager.getStatistics().getHitCount());
        assertEquals(1, cacheManager.getStatistics().getMissCount());
        
        // Put data and hit
        cacheManager.refresh();
        cacheManager.get();
        assertEquals(1, cacheManager.getStatistics().getHitCount());
        assertEquals(1, cacheManager.getStatistics().getMissCount());
    }
    
    @Test
    void testRefreshWithException() {
        CacheDataLoader<List<String>> failingLoader = () -> {
            throw new RuntimeException("Data loading failed");
        };
        cacheManager.setDataLoader(failingLoader);
        
        assertThrows(RuntimeException.class, () -> cacheManager.refresh());
    }
}
