<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        https://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/aop
        https://www.springframework.org/schema/aop/spring-aop.xsd">

    <bean id="entryAspect" class="com.sfn.commonlogging.aspect.MethodEntryAspect"></bean>
    <bean id="exitAspect" class="com.sfn.commonlogging.aspect.MethodExitAspect"></bean>

    <aop:config>
        <aop:pointcut id="logAllMethods" expression="within(${base.package}..*)" />
        <aop:aspect id="logentryaspect" ref="entryAspect" >
            <aop:before method="traceLogger" pointcut-ref="logAllMethods" />
        </aop:aspect>
        <aop:aspect id="logexitaspect" ref="exitAspect" >
            <aop:after method="traceLogger" pointcut-ref="logAllMethods" />
        </aop:aspect>
    </aop:config>

</beans>