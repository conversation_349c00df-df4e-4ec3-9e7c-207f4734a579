<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
        https://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/aop
        https://www.springframework.org/schema/aop/spring-aop.xsd">

	<bean id="latencyTimerAspect"
		class="com.sfn.commonlogging.aspect.LatencyTimerAspect"></bean>

	<aop:config>
		<!--<aop:pointcut id="logAllMethods" expression="within(${base.package}..*)" 
			/> -->
		<aop:pointcut id="logLatencyInMethods"
			expression="@annotation(com.sfn.commonlogging.annotation.RecordLatency)" />
		<aop:pointcut id="logTheLatency"
			expression="@annotation(com.sfn.commonlogging.annotation.PrintLatency)" />

		<aop:aspect id="loglatencyaspect" ref="latencyTimerAspect">
			<aop:around method="latencyLogger"
				pointcut-ref="logLatencyInMethods" />
		</aop:aspect>

		<aop:aspect id="printlatencyaspect"
			ref="latencyTimerAspect">
			<aop:around method="latencyLoggerPrinter"
				pointcut-ref="logTheLatency" />
			<aop:after-throwing method="latencyLoggerPrinterForException"
				pointcut-ref="logTheLatency" throwing="ex"/>
		</aop:aspect>
	</aop:config>

</beans>