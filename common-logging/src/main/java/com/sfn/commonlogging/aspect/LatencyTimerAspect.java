package com.sfn.commonlogging.aspect;

import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.ThreadContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.CodeSignature;

import com.fasterxml.jackson.databind.ObjectMapper;

public class LatencyTimerAspect {
    private static final Logger logger = LogManager.getLogger();
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public Object latencyLogger(ProceedingJoinPoint joinPoint) throws Throwable {

        long startTime = System.currentTimeMillis();
        Object response = joinPoint.proceed();
        long endTime = System.currentTimeMillis();
        ThreadContext.put(getSimpleClassName(joinPoint), ""+(endTime - startTime));
        logger.debug("Execution time taken by the method: [{}] = [{} ms]", getSimpleClassName(joinPoint), endTime - startTime);

        return response;
    }

    public Object latencyLoggerPrinter(ProceedingJoinPoint joinPoint) throws Throwable {
        Object response = latencyLogger(joinPoint);
        Map<String, String> threadContextMap = ThreadContext.getContext();
        
        try {
        	
        	String json = objectMapper.writeValueAsString(threadContextMap);
			 logger.debug("[PERFM] : {}", json);
		} catch (Exception e) {
			logger.error("Error logging DTO as JSON: {}", e.getMessage());
		}
        
       
        return response;
    }
    
    
    public Exception latencyLoggerPrinterForException(Exception ex) throws Throwable {
        Map<String, String> threadContextMap = ThreadContext.getContext();
        
        try {
        	String json = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(threadContextMap);
			logger.debug("[PERFM] : {}", json);
		} catch (Exception e) {
			logger.error("Error logging DTO as JSON: {}", e.getMessage());
		}
        return ex;
    }

    private String getSimpleClassName(ProceedingJoinPoint joinPoint) {
        CodeSignature codeSignature = (CodeSignature) joinPoint.getSignature();

        String[] packageNames = codeSignature.getDeclaringTypeName().split("[.]");
        int size = packageNames.length;
        return packageNames[size-2] + "." + packageNames[size-1] + "." + codeSignature.getName();
    }
}
