package com.sfn.configuration;

import com.sfn.service.MessageProcessor;
import com.sfn.service.impl.ActiveMQConsumerService;
import jakarta.jms.ConnectionFactory;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;

import java.util.Arrays;

@Configuration
public class ActiveMQConsumerConfig {

	@Value("${notification.active-mq.broker-url}")
	private String brokerUrl;

	@Value("${notification.active-mq.queue}")
	private String queue;

	@Autowired
	MessageProcessor messageProcessor;

	@Bean
	public ConnectionFactory connectionFactory(){
		ActiveMQConnectionFactory activeMQConnectionFactory  = new ActiveMQConnectionFactory();
		activeMQConnectionFactory.setBrokerURL(brokerUrl);
		activeMQConnectionFactory.setTrustedPackages(Arrays.asList("com.mailshine.springbootstandaloneactivemq"));
		return  activeMQConnectionFactory;
	}

	@Bean
	public DefaultJmsListenerContainerFactory jmsListenerContainerFactory(){
	    DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
	    factory.setConnectionFactory(connectionFactory());
	    return factory;
	}

	@Bean
	public ActiveMQConsumerService consumerService() {
		return new ActiveMQConsumerService(queue, messageProcessor);
	}
}