package com.sfn.configuration;

import java.util.Arrays;

import com.sfn.service.impl.ActiveMQProducerService;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
import org.springframework.jms.core.JmsTemplate;

import jakarta.jms.ConnectionFactory;

@Configuration
public class ActiveMQProducerConfig {

	@Value("${notification.active-mq.queue}")
	private String queues;

	@Value("${notification.active-mq.broker-url}")
	private String brokerUrl;

	@Bean
	public ConnectionFactory connectionFactory(){
		ActiveMQConnectionFactory activeMQConnectionFactory  = new ActiveMQConnectionFactory();
		activeMQConnectionFactory.setBrokerURL(brokerUrl);
		activeMQConnectionFactory.setTrustedPackages(Arrays.asList("com.mailshine.springbootstandaloneactivemq"));
		return  activeMQConnectionFactory;
	}

	@Bean
	public JmsTemplate jmsTemplate(){
	    JmsTemplate jmsTemplate = new JmsTemplate();
	    jmsTemplate.setConnectionFactory(connectionFactory());
	    return jmsTemplate;
	}

	@Bean
	public DefaultJmsListenerContainerFactory jmsListenerContainerFactory(){
	    DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
	    factory.setConnectionFactory(connectionFactory());
	    return factory;
	}

	@Bean
	public ActiveMQProducerService producerService() {
		return new ActiveMQProducerService(queues, jmsTemplate());
	}
}