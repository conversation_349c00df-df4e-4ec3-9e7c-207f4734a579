package com.sfn.service.impl;

import com.sfn.service.MessageProcessor;
import jakarta.jms.JMSException;
import org.apache.activemq.command.ActiveMQMapMessage;
import org.springframework.jms.annotation.JmsListenerConfigurer;
import org.springframework.jms.config.JmsListenerEndpointRegistrar;
import org.springframework.jms.config.SimpleJmsListenerEndpoint;

import java.util.Map;

public class ActiveMQConsumerService implements JmsListenerConfigurer {

	private String queue;
	private MessageProcessor messageProcessor;

	public ActiveMQConsumerService(String queue, MessageProcessor messageProcessor) {
		this.queue = queue;
		this.messageProcessor = messageProcessor;
	}

	@Override
	public void configureJmsListeners(JmsListenerEndpointRegistrar registrar) {
		SimpleJmsListenerEndpoint endpoint = new SimpleJmsListenerEndpoint();
		endpoint.setId(queue);
		endpoint.setDestination(queue);
		endpoint.setMessageListener(message -> {
			System.out.println(message);
			if(message instanceof ActiveMQMapMessage) {
				ActiveMQMapMessage textMessage = (ActiveMQMapMessage)message;
				Map<String, Object> messageData = null;
				try {
					messageData = textMessage.getContentMap();
					System.out.println(messageData);
				} catch (JMSException e) {
					throw new RuntimeException(e);
				}
				messageProcessor.process(messageData);
				System.out.println("messageData:"+messageData.get("eventName"));
			}
		});
		registrar.registerEndpoint(endpoint);
	}
}