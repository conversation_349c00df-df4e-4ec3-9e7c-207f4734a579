package com.sfn.service.impl;

import com.sfn.constants.ResponseStatus;
import org.apache.coyote.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jms.JmsException;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Service;

import com.sfn.constants.ApiResponseCode;
import com.sfn.constants.Result;
import com.sfn.exception.ActiveMqException;
import com.sfn.service.JmsProducerService;

public class ActiveMQProducerService<T> implements JmsProducerService<T> {

	JmsTemplate jmsTemplate;

	private String queues;

	public ActiveMQProducerService(String queues, JmsTemplate jmsTemplate) {
		this.queues = queues;
		this.jmsTemplate = jmsTemplate;
	}

	@Override
	public ResponseStatus publishMessage(T message) {
		try{
			String[] queueList = queues.split(",");
			for(String queueName : queueList) {
				jmsTemplate.convertAndSend(queueName, message);
			}
			return ResponseStatus.SUCCESS;
		} catch(JmsException exception){
			throw new ActiveMqException("jms exception occured in publishing message: ".concat(exception.getMessage()),Result.FAILED,ApiResponseCode.MESSAGE_PUBLISH_FAILED);
		} catch(Exception exception) {
			throw new ActiveMqException("exception occured while sending message: ".concat(exception.getMessage()),Result.FAILED,ApiResponseCode.INTERNAL_SERVER_ERROR);
		}
	}

	public void setQueues(String queue) {
		this.queues = queue;
	}

	public String getQueues() {
		return queues;
	}
}