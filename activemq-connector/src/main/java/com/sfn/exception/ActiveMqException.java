package com.sfn.exception;

public class ActiveMqException extends RuntimeException{

	private String result;
	private String responseCode;

	private static final long serialVersionUID = 1L;

	public ActiveMqException(String exception,String result,String responseCode) {
		super(exception);
		this.result = result;
		this.responseCode = responseCode;		
	}

	public String getResult() {
		return result;
	}

	public String getResponseCode() {
		return responseCode;
	}	
}