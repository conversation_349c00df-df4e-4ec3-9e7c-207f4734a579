# activemq-connector

## Desccription
This library abstracts the boilerplate code of writing a producer and consumer code in any service

## How to use
### Producer / Publisher
### Consumer / Subscriber
- Enable the application to process JMS messaging by adding the below annotation on any configuration class
```java
@EnableJms
```
- Import the config class to load the resources
  Annotate the configuration class to import the config class
```java
@Import(value=com.sfn.configuration.ActiveMQConsumerConfig.class)
```
- Define required properties
```properties
notification.active-mq.broker-url=tcp://<brokerip>:61616
notification.active-mq.queue=<name of the queue>
```
- Provide message processor Implementation
```java
public class QueueMessageProcessorImpl implements MessageProcessor<Map<String, Object>> 
```
