# Centralized Tracer

## Desccription
This library abstracts the boilerplate code of having a filter which assigns the tracking id of each and every request.
The filter checks for the request, if the request has a header X-TRACE-ID, then uses the same value as tracking id for that flow.
If the header doesnt have that value, then the filter creates a new tracking id for the flow in the below format
```
<PRE>-UUID
```
The filter also sends the assigned tracking id in the request header originating from the service

## How to use
### Import the library
- Add the centralized-tracing dependency to the maven pom
```xml
<dependency>
  <groupId>com.sfn</groupId>
  <artifactId>centralized-tracer</artifactId>
  <version>0.0.1-SNAPSHOT</version>
</dependency>
```
### Load the filter
- Import the config class to load the filter
  Implement the below configuration to load a bean for the filter

```java
import com.sfn.filter.TraceFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import(value = com.sfn.filter.TraceFilter.class)
public class TraceFilterConfig {

  private TraceFilter traceFilter;

  public TraceFilterConfig(TraceFilter traceFilter) {
    this.traceFilter = traceFilter;
  }

  @Bean
  public FilterRegistrationBean<TraceFilter> loggingFilter() {
    FilterRegistrationBean<TraceFilter> registrationBean = new FilterRegistrationBean<>();

    registrationBean.setFilter(traceFilter);
    registrationBean.addUrlPatterns("/*");

    return registrationBean;
  }

}
```
### Define the properties
- Define required properties as per the application
```properties
logtracer=NPR
```


### Configure the interceptor into rest template
- Configure the resttemplate with the listed interceptor as below

```java
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {

        return builder
            .interceptors(List.of(new TrackingInterceptor()))
            .build();
    }
```