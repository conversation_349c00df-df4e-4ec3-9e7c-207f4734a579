package com.sfn.filter;


import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.logging.log4j.ThreadContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.UUID;

@Component
public class TraceFilter implements Filter {
    protected final Log logger = LogFactory.getLog(this.getClass());

    private final String X_TRACE_ID = "X-TRACE-ID";


    private String prefix;

    public TraceFilter(@Value("${logtracer}") String prefix) {
        this.prefix = prefix;
    }

     @Override
     public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
             throws IOException, ServletException {

         // Generate a unique tracking ID
         HttpServletRequest httpServletRequest = (HttpServletRequest) request;
         HttpServletResponse httpServletResponse = (HttpServletResponse) response;
         String trackingId = httpServletRequest.getHeader(X_TRACE_ID);

         if(trackingId == null)
             trackingId = prefix + "-" + UUID.randomUUID().toString();

         ThreadContext.put("log-trace-id", trackingId);

         // Add the tracking ID to the response headers
         httpServletResponse.setHeader(X_TRACE_ID, trackingId);

         // Continue with the filter chain
         chain.doFilter(request, response);
     }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        if (prefix == null) {
            prefix = "DEF"; // Default value if not set
        }
        logger.info("Initialized with prefix: " + prefix);
    }

     @Override
     public void destroy() {
         // Cleanup code, if needed
     }
 }

