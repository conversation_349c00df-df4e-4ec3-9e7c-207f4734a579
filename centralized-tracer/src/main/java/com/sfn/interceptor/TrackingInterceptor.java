package com.sfn.interceptor;

import org.apache.logging.log4j.ThreadContext;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;

public class TrackingInterceptor implements ClientHttpRequestInterceptor {
    private static final String X_TRACE_ID = "X-TRACE-ID";
    private static final String LOG_TRACER_ID = "log-trace-id";

    @Override
    public ClientHttpResponse intercept(
            HttpRequest request, byte[] body,
            ClientHttpRequestExecution execution) throws IOException {

        String trackingId = ThreadContext.get(LOG_TRACER_ID);

        if (trackingId != null) {
            request.getHeaders().add(X_TRACE_ID, trackingId);
        }

        return execution.execute(request, body);
    }
}

