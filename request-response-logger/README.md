# Request Response Logger

## Description
This library simplifies request and response logging in Spring Boot applications by providing ready-to-use components that eliminate boilerplate code. It offers:

1. A servlet filter that automatically captures and logs incoming HTTP request payloads and outgoing response payloads
2. A RestTemplate interceptor for logging external API calls with complete request/response details
3. Configurable logging formats with JSON parsing for better readability

All logs are written to your application's configured log file, making it easy to trace and debug API interactions without modifying your business logic.

## How to use
### Import the library
- Add the request-response-logger dependency to the maven pom
```xml
<dependency>
  <groupId>com.sfn</groupId>
  <artifactId>request-response-logger</artifactId>
  <version>1.0.0-SNAPSHOT</version>
</dependency>
```
- Required dependencies
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>
<dependency>
    <groupId>jakarta.servlet</groupId>
    <artifactId>jakarta.servlet-api</artifactId>
</dependency>
<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-api</artifactId>
</dependency> 
```

### Configure Logging
Enable the logging for the filter package in your logback.xml or log4j2.xml:
```xml
<Logger name="com.sfn" level="info" additivity="false">
    <AppenderRef ref="LogToRollingFile"/>
    <AppenderRef ref="LogToConsole"/>
</Logger>
```

## Implementation
### Request Response Filter
Import and configure the filter to log all incoming HTTP requests and responses:

```java
import com.sfn.filter.LogFilter;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Log4j2
@Import(value = LogFilter.class)
public class RequestResponseLogFilterConfig {

  private LogFilter logFilter;

  public RequestResponseLogFilterConfig(LogFilter logFilter) {
    this.logFilter = logFilter;
  }

  @Bean
  public FilterRegistrationBean<LogFilter> requestReponseLoggingFilter() {
    FilterRegistrationBean<LogFilter> registrationBean = new FilterRegistrationBean<>();

    registrationBean.setFilter(logFilter);
    registrationBean.addUrlPatterns("/*");

    return registrationBean;
  }
}
```

### RestTemplate Logging Interceptor
Configure a custom RestTemplate with the logging interceptor for external API calls:

```java
import com.sfn.filter.RestTemplateLoggingInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Qualifier;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateConfig {

  @Bean
  public RestTemplateLoggingInterceptor restTemplateLoggingInterceptor() {
    return new RestTemplateLoggingInterceptor();
  }

  @Bean
  @Qualifier("loggingRestTemplate")
  public RestTemplate loggingRestTemplate(RestTemplateLoggingInterceptor interceptor) {
    RestTemplate restTemplate = new RestTemplate();
    restTemplate.getInterceptors().add(interceptor);
    return restTemplate;
  }
  
  @Bean
  @Primary
  public RestTemplate defaultRestTemplate() {
    return new RestTemplate();
  }
}
```

Use the configured RestTemplate in your service with @Qualifier:
```java
@Service
public class ApiService {
  
  @Autowired
  @Qualifier("loggingRestTemplate")
  private RestTemplate loggingRestTemplate;
  
  public void callExternalApi() {
    // This call will be automatically logged with request/response details
    loggingRestTemplate.getForObject("https://api.example.com/data", String.class);
  }
}
```