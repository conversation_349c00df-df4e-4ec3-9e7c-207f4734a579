package com.sfn.filter;

public class RequestResponseLogger {

    private Object request;
    private String response;
    private int latency;

    public Object getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request.replaceAll("\n", "");
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public int getLatency() {
        return latency;
    }

    public void setLatency(long latency) {
        this.latency = (int) latency;
    }
}
