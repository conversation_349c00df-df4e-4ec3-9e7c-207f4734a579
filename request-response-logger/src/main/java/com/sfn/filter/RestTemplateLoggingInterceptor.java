package com.sfn.filter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class RestTemplateLoggingInterceptor implements ClientHttpRequestInterceptor {
    protected final Log logger = LogFactory.getLog(this.getClass());
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
        Map<String, Object> loggerObj = new HashMap<>();
        loggerObj.put("URL", request.getURI());
        loggerObj.put("request", parseJsonSafely(new String(body, StandardCharsets.UTF_8)));
        long startTime = System.currentTimeMillis();

        ClientHttpResponse response = execution.execute(request, body);

        loggerObj.put("latency", System.currentTimeMillis() - startTime);
        loggerObj.put("status", response.getStatusCode().value());
        String responseBody = new BufferedReader(new InputStreamReader(response.getBody(), StandardCharsets.UTF_8))
                .lines().collect(Collectors.joining("\n"));
        loggerObj.put("response", parseJsonSafely(responseBody));

        String data = null;
        try {
            data = objectMapper.writeValueAsString(loggerObj);
        } catch (JsonProcessingException e) {
            logger.info("Exception in request logger " + e.getMessage());
            throw new RuntimeException(e);
        }
        logger.info("external request stats " + data);
        return response;
    }

    private Object parseJsonSafely(String content) {
        try {
            return objectMapper.readTree(content);
        } catch (JsonProcessingException e) {
            logger.info("Exception in parseJsonSafely " + e.getMessage());
            return content; // Return raw string if it's not valid JSON
        }
    }
}