package com.sfn.filter;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Component
public class LogFilter implements Filter {

    protected final Log logger = LogFactory.getLog(this.getClass());
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        Map<String, Object> loggerObj = new HashMap<>();
        ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper((HttpServletRequest) request);
        ContentCachingResponseWrapper wrappedResponse = new ContentCachingResponseWrapper((HttpServletResponse) response);

        long startTime = System.currentTimeMillis();
        chain.doFilter(wrappedRequest,wrappedResponse);
        loggerObj.put("latency", System.currentTimeMillis() - startTime);
        loggerObj.put("URL", wrappedRequest.getRequestURI());

        String data = null;
        try {
            loggerObj.put("request", parseJsonSafely(getContentFromByteArray(wrappedRequest.getContentAsByteArray())));
            loggerObj.put("response", parseJsonSafely(getContentFromByteArray(wrappedResponse.getContentAsByteArray())));
            data = objectMapper.writeValueAsString(loggerObj);
        } catch (JsonProcessingException e) {
            logger.info("Exception in request logger " + e.getMessage());
            throw new RuntimeException(e);
        }

        logger.info("request stats : " + data);
        wrappedResponse.copyBodyToResponse();

    }

    private Object parseJsonSafely(String content) {
        try {
            return objectMapper.readTree(content);
        } catch (JsonProcessingException e) {
            logger.info("Exception in parseJsonSafely " + e.getMessage());
            return content; // Return raw string if it's not valid JSON
        }
    }

    private String getContentFromByteArray(byte[] content) {
        return new String(content, StandardCharsets.UTF_8).trim();
    }
 }